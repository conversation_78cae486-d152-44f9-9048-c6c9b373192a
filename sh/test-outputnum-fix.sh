#!/bin/bash

# 测试 outputNum 参数修复效果的脚本

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}  测试 outputNum 参数修复效果${NC}"
echo -e "${BLUE}========================================${NC}"
echo ""

BASE_URL="http://localhost:18088"

# 检查服务器状态
echo -e "${YELLOW}1. 检查服务器状态...${NC}"
http_code=$(curl -s -o /dev/null -w "%{http_code}" "$BASE_URL/api/clothing/health" --connect-timeout 5)

if [ "$http_code" = "200" ]; then
    echo -e "${GREEN}✅ 服务器运行正常${NC}"
else
    echo -e "${RED}❌ 服务器未运行或无法访问 (HTTP $http_code)${NC}"
    echo ""
    echo "🚀 启动服务器的方法:"
    echo "1. 使用Maven: mvn spring-boot:run"
    echo "2. 使用jar包: java -jar target/ziniao-ai-demo-1.0.0.jar"
    echo "3. 使用IDE: 运行 ZiniaoAiDemoApplication.main()"
    echo ""
    echo "请先启动服务器，然后重新运行此测试脚本"
    exit 1
fi
echo ""

# 测试1: outputNum=1 (默认值)
echo -e "${YELLOW}2. 测试 outputNum=1 (默认值)...${NC}"

request_body_1=$(cat <<EOF
{
    "upperOriginUrl": "https://cdn.linkfox.com/ai-site/test/workbench/upper-true5.webp",
    "upperImageUrl": "",
    "downOriginUrl": "",
    "downImageUrl": "",
    "modelImageUrl": "https://test-file-ai.linkfox.com/UPLOAD/MANAGE/338dd42718f1436a8b2e1d494e80422a.png",
    "modelMaskImageUrl": "",
    "outputNum": 1
}
EOF
)

echo "请求参数:"
echo "$request_body_1" | jq . 2>/dev/null || echo "$request_body_1"
echo ""

echo "🚀 发送请求..."
response_1=$(curl -s -w "\nHTTP_CODE:%{http_code}" \
    -X POST "$BASE_URL/api/clothing/fittingRoom" \
    -H "Content-Type: application/json" \
    -d "$request_body_1" \
    --max-time 30 2>/dev/null)

http_code_1=$(echo "$response_1" | tail -n1 | cut -d: -f2)
response_body_1=$(echo "$response_1" | sed '$d')

echo "HTTP状态码: $http_code_1"
echo "响应内容:"
echo "$response_body_1" | jq . 2>/dev/null || echo "$response_body_1"
echo ""

# 提取任务ID
if [ "$http_code_1" = "200" ]; then
    if command -v jq &> /dev/null; then
        task_id_1=$(echo "$response_body_1" | jq -r '.data.data.id // empty')
        if [ -n "$task_id_1" ] && [ "$task_id_1" != "null" ]; then
            echo -e "${GREEN}✅ 任务提交成功，任务ID: $task_id_1${NC}"
        else
            echo -e "${RED}❌ 无法提取任务ID${NC}"
            task_id_1=""
        fi
    else
        echo -e "${YELLOW}⚠️ 需要安装 jq 来解析JSON响应${NC}"
        task_id_1=""
    fi
else
    echo -e "${RED}❌ 任务提交失败 (HTTP $http_code_1)${NC}"
    task_id_1=""
fi
echo ""

# 测试2: outputNum=3 (多张图片)
echo -e "${YELLOW}3. 测试 outputNum=3 (多张图片)...${NC}"

request_body_3=$(cat <<EOF
{
    "upperOriginUrl": "https://cdn.linkfox.com/ai-site/test/workbench/upper-true5.webp",
    "upperImageUrl": "",
    "downOriginUrl": "",
    "downImageUrl": "",
    "modelImageUrl": "https://test-file-ai.linkfox.com/UPLOAD/MANAGE/338dd42718f1436a8b2e1d494e80422a.png",
    "modelMaskImageUrl": "",
    "outputNum": 3
}
EOF
)

echo "请求参数:"
echo "$request_body_3" | jq . 2>/dev/null || echo "$request_body_3"
echo ""

echo "🚀 发送请求..."
response_3=$(curl -s -w "\nHTTP_CODE:%{http_code}" \
    -X POST "$BASE_URL/api/clothing/fittingRoom" \
    -H "Content-Type: application/json" \
    -d "$request_body_3" \
    --max-time 30 2>/dev/null)

http_code_3=$(echo "$response_3" | tail -n1 | cut -d: -f2)
response_body_3=$(echo "$response_3" | sed '$d')

echo "HTTP状态码: $http_code_3"
echo "响应内容:"
echo "$response_body_3" | jq . 2>/dev/null || echo "$response_body_3"
echo ""

# 提取任务ID
if [ "$http_code_3" = "200" ]; then
    if command -v jq &> /dev/null; then
        task_id_3=$(echo "$response_body_3" | jq -r '.data.data.id // empty')
        if [ -n "$task_id_3" ] && [ "$task_id_3" != "null" ]; then
            echo -e "${GREEN}✅ 任务提交成功，任务ID: $task_id_3${NC}"
        else
            echo -e "${RED}❌ 无法提取任务ID${NC}"
            task_id_3=""
        fi
    else
        echo -e "${YELLOW}⚠️ 需要安装 jq 来解析JSON响应${NC}"
        task_id_3=""
    fi
else
    echo -e "${RED}❌ 任务提交失败 (HTTP $http_code_3)${NC}"
    task_id_3=""
fi
echo ""

# 等待任务完成并查询结果
if [ -n "$task_id_1" ] || [ -n "$task_id_3" ]; then
    echo -e "${YELLOW}4. 等待任务完成并查询结果...${NC}"
    echo "⏰ 等待30秒让任务处理..."
    sleep 30
    echo ""
    
    # 查询 outputNum=1 的结果
    if [ -n "$task_id_1" ]; then
        echo -e "${BLUE}查询 outputNum=1 的结果 (任务ID: $task_id_1)...${NC}"
        result_1=$(curl -s "$BASE_URL/api/clothing/result/$task_id_1" --max-time 10 2>/dev/null)
        echo "结果:"
        echo "$result_1" | jq . 2>/dev/null || echo "$result_1"
        
        if command -v jq &> /dev/null; then
            result_images_1=$(echo "$result_1" | jq -r '.data.resultImages // empty')
            result_image_1=$(echo "$result_1" | jq -r '.data.resultImage // empty')
            if [ -n "$result_images_1" ] && [ "$result_images_1" != "null" ]; then
                image_count_1=$(echo "$result_1" | jq -r '.data.resultImages | length')
                echo -e "${GREEN}✅ 返回了 $image_count_1 张图片${NC}"
            elif [ -n "$result_image_1" ] && [ "$result_image_1" != "null" ]; then
                echo -e "${GREEN}✅ 返回了 1 张图片（兼容模式）${NC}"
            else
                echo -e "${YELLOW}⚠️ 任务可能还在处理中${NC}"
            fi
        fi
        echo ""
    fi
    
    # 查询 outputNum=3 的结果
    if [ -n "$task_id_3" ]; then
        echo -e "${BLUE}查询 outputNum=3 的结果 (任务ID: $task_id_3)...${NC}"
        result_3=$(curl -s "$BASE_URL/api/clothing/result/$task_id_3" --max-time 10 2>/dev/null)
        echo "结果:"
        echo "$result_3" | jq . 2>/dev/null || echo "$result_3"
        
        if command -v jq &> /dev/null; then
            result_images_3=$(echo "$result_3" | jq -r '.data.resultImages // empty')
            result_image_3=$(echo "$result_3" | jq -r '.data.resultImage // empty')
            if [ -n "$result_images_3" ] && [ "$result_images_3" != "null" ]; then
                image_count_3=$(echo "$result_3" | jq -r '.data.resultImages | length')
                echo -e "${GREEN}✅ 返回了 $image_count_3 张图片${NC}"
                if [ "$image_count_3" -gt 1 ]; then
                    echo -e "${GREEN}🎉 outputNum 参数修复成功！${NC}"
                else
                    echo -e "${YELLOW}⚠️ 仍然只返回了1张图片，可能需要进一步检查${NC}"
                fi
            elif [ -n "$result_image_3" ] && [ "$result_image_3" != "null" ]; then
                echo -e "${YELLOW}⚠️ 只返回了 1 张图片（兼容模式），修复可能未生效${NC}"
            else
                echo -e "${YELLOW}⚠️ 任务可能还在处理中${NC}"
            fi
        fi
        echo ""
    fi
fi

# 总结
echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}  测试总结${NC}"
echo -e "${BLUE}========================================${NC}"
echo ""

echo "🔍 修复内容:"
echo "1. ✅ 修改了 ClothingResponse.ClothingData 支持多张图片"
echo "2. ✅ 添加了 resultImages 字段存储图片列表"
echo "3. ✅ 修改了 ResultService 收集所有成功的图片"
echo "4. ✅ 保持向后兼容，resultImage 字段仍然存在"
echo ""

echo "📋 使用说明:"
echo "1. 提交任务时设置 outputNum 参数（1-4）"
echo "2. 使用 /api/clothing/result/{taskId} 查询结果"
echo "3. 检查响应中的 resultImages 字段获取所有图片"
echo "4. resultImage 字段包含第一张图片（向后兼容）"
echo ""

echo "🔗 相关接口:"
echo "- 提交任务: POST $BASE_URL/api/clothing/fittingRoom"
echo "- 查询结果: GET $BASE_URL/api/clothing/result/{taskId}"
echo "- API文档: $BASE_URL/doc.html"
echo ""

echo "测试完成！"
